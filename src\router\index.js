import { createRouter, createWebHistory } from "vue-router";
import { useUserStore } from "@/stores/user";

import LoginPage from "@/views/LoginPage.vue";
import HomePage from "@/views/HomePage.vue";
import SectionListPage from "@/views/SectionListPage.vue";
import PostListPage from "@/views/PostListPage.vue";
import PostDetailPage from "@/views/PostDetailPage.vue";
import CreatePostPage from "@/views/CreatePostPage.vue";
import NotFoundPage from "@/views/NotFoundPage.vue";
import test from "@/views/test.vue";

const routes = [
    {
        path: '/',
        component: HomePage,
        children: [
            {
                path: '',
                component: SectionListPage
            },
            {
                path: 'section/:sectionId',
                component: PostListPage
            },
            {
                path: 'posts',
                component: PostListPage
            },
            {
                path: 'post/:id',
                component: PostDetailPage
            },
            {
                path: 'post/create',
                component: CreatePostPage
            }
        ]
    },
    {path: '/login', component: LoginPage},
    {path: '/test', component: test},
    {path: '/404', component: NotFoundPage},
    {path: '/:pathMatch(.*)*', redirect: '/404'}
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore();

    // 如果有token但没有用户信息，尝试获取用户信息
    if (userStore.token && !userStore.user) {
        await userStore.initUser();
    }

    next();
});

export default router;
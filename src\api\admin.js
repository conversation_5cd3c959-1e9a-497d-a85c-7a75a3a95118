import request from './request'

export const adminApi = {
  // 设置帖子精华
  setPostFeatured(id, data) {
    return request({
      url: `/admin/posts/${id}/feature`,
      method: 'PUT',
      data
    })
  },

  // 设置帖子置顶
  setPostPinned(id, data) {
    return request({
      url: `/admin/posts/${id}/pin`,
      method: 'PUT',
      data
    })
  },

  // 更新用户积分
  updateUserPoints(id, data) {
    return request({
      url: `/admin/users/${id}/points`,
      method: 'PUT',
      data
    })
  }
}
